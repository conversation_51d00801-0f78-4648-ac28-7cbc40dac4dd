//package com.flutter.marketstreamcomparisontool.client;
//
//import com.ppb.platform.sb.metrics.mantis.MantisMonitorDispatcher;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.ImportResource;
//import org.springframework.context.annotation.PropertySource;
//import org.springframework.context.support.PropertySourcesPlaceholderConfigurer;
//
//@Configuration
//@PropertySource({"classpath:application.properties", "classpath:performance.properties"})
//@ImportResource("classpath:conf/fms-client-monitor-config.xml")
//public class AppConfig {
//
//    @Autowired
//    @Qualifier("fms.client.notification.metrics.monitorDispatcher")
//    private MantisMonitorDispatcher dispatcher;
//
//
//    @Bean
//    public static PropertySourcesPlaceholderConfigurer propertySourcesPlaceholderConfigurer() {
//        return new PropertySourcesPlaceholderConfigurer();
//    }
//}
