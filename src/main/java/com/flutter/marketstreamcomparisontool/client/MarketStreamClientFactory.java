//package com.flutter.marketstreamcomparisontool.client;
//
//import com.betfair.mantis.performance.PerformanceDisruptor;
//import com.ppb.platform.sb.fmg.MarketStreamClient;
//import com.ppb.platform.sb.metrics.mantis.MantisMonitorDispatcher;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.ImportResource;
//
//@ImportResource("classpath:conf/fms-new-client-application.xml")
//public class MarketStreamClientFactory {
//
//    public static MarketStreamClient createClient(
//            String clientConfig,
//            String consumerConfig,
//            String consumerContainerConfig,
//            @Autowired final PerformanceDisruptor performanceDisruptor
//    ) throws Exception {
//        return new MarketStreamClient.MarketStreamClientBuilder(clientConfig)
//                .setProvidedConsumerConfig(consumerConfig)
//                .setProvidedConsumerContainerConfig(consumerContainerConfig)
//                .setMonitorDispatcher(new MantisMonitorDispatcher(performanceDisruptor))
//                .build();
//    }
//}
