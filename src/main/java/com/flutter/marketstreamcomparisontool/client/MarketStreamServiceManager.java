package com.flutter.marketstreamcomparisontool.client;

import com.betfair.platform.fms.cache.KafkaMarketViewCache;
import com.ppb.platform.sb.fmg.MarketStreamClient;
import com.ppb.platform.sb.toggle.feature.FeatureToggle;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.SmartLifecycle;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.google.common.base.Preconditions.checkArgument;

@Slf4j
@Component
public class MarketStreamServiceManager implements SmartLifecycle {

    private static final int PHASE = 0;

    private final MarketStreamClient marketStreamClientGSSP;
    private final MarketStreamClient marketStreamClientFMG;
    private final FeatureToggle marketStreamClientToggleGSSP;
    private final FeatureToggle consumerToggleFMG;

    public MarketStreamServiceManager(MarketStreamClient marketStreamClientGSSP,
                                      MarketStreamClient marketStreamClientFMG,
                                      FeatureToggle marketStreamClientToggleGSSP,
                                      FeatureToggle marketStreamClientToggleFMG) {
        checkArgument(marketStreamClientGSSP != null, "marketStreamClientGSSP is mandatory");
        checkArgument(marketStreamClientFMG != null, "marketStreamClientFMG is mandatory");
        checkArgument(marketStreamClientToggleGSSP != null, "marketStreamClientToggleGSSP is mandatory");
        checkArgument(marketStreamClientToggleFMG != null, "marketStreamClientToggleFMG is mandatory");
        this.marketStreamClientGSSP = marketStreamClientGSSP;
        this.marketStreamClientFMG = marketStreamClientFMG;
        this.marketStreamClientToggleGSSP = marketStreamClientToggleGSSP;
        this.consumerToggleFMG = marketStreamClientToggleFMG;
    }

    @Override
    public void stop(@NotNull Runnable runnable) {
        this.stop();
    }

    @Override
    public void start() {
        log.info("operation='start', msg='starting market stream cache service'");

        Set<KafkaMarketViewCache> marketStreamClients = new HashSet<>();

        if (marketStreamClientToggleGSSP.isEnabled()) {
            marketStreamClients.add(marketStreamClientGSSP);
        }

//        if (consumerToggleFMG.isEnabled()) {
//            marketStreamClients.add(marketStreamClientFMG);
//        }

        ExecutorService executorService = Executors.newFixedThreadPool(marketStreamClients.size());

        List<Future<?>> futures = marketStreamClients.stream()
                .map((client) -> executorService.submit(() -> {
                    try {
                        client.start();
                    } catch (Exception e) {
                        log.error("operation='start', msg='failed starting market stream cache service'", e);
                    }
                    return null;
                }))
                .collect(Collectors.toList());


        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("operation='start', msg='failed starting market stream cache service'", e);
                throw new RuntimeException(e);
            }
        }

        executorService.shutdown();
    }

    @Override
    public void stop() {
        log.info("operation='stop', msg='stopping market stream cache service'");
        if (marketStreamClientGSSP.isRunning()) {
            marketStreamClientGSSP.stop();
        }
        if (marketStreamClientFMG.isRunning()) {
            marketStreamClientFMG.stop();
        }
    }

    @Override
    public boolean isRunning() {
        return (!marketStreamClientToggleGSSP.isEnabled() || marketStreamClientGSSP.isRunning()) &&
                (!consumerToggleFMG.isEnabled() || marketStreamClientFMG.isRunning());

    }

    @Override
    public int getPhase() {
        return PHASE;
    }
}

