package com.flutter.marketstreamcomparisontool.client;

import com.betfair.mantis.performance.ClassBasedPredicate;
import com.betfair.mantis.performance.PerformanceDisruptor;
import com.betfair.mantis.performance.ServiceDataEventHandler;
import com.ppb.platform.sb.fmg.MarketStreamClient;
import com.ppb.platform.sb.metrics.mantis.MantisMonitorDispatcher;
import com.ppb.platform.sb.toggle.feature.FeatureToggle;
import com.ppb.platform.sb.toggle.feature.SimpleFeatureToggle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportResource;
import org.springframework.context.support.PropertySourcesPlaceholderConfigurer;
import org.springframework.core.io.ClassPathResource;

import java.util.HashSet;
import java.util.Set;

import static com.betfair.mantis.performance.PerformanceEvent.PerfEventClass.SERVICE;


@Configuration
/* Needed to import a performanceDisruptor bean required by the MarketStreamClient */
//@ImportResource("classpath:conf/fms-new-client-application.xml")
@ImportResource("classpath:performance-logging-context-common.xml")
public class MarketStreamConfiguration {

    @Bean
    public MarketStreamClient marketStreamClientGSSP(@Value("${msct.market.stream.client.config.gssp}") final String marketStreamClientConfigGSSP,
                                                     @Value("${msct.market.stream.consumer.config.gssp}") final String marketStreamConsumerConfigGSSP,
                                                     @Value("${msct.market.stream.consumer.container.config.gssp}") final String marketStreamConsumerContainerConfigGSSP,
                                                     @Autowired final PerformanceDisruptor performanceDisruptor) throws Exception {
        return new MarketStreamClient.MarketStreamClientBuilder(marketStreamClientConfigGSSP)
                .setProvidedConsumerConfig(marketStreamConsumerConfigGSSP)
                .setProvidedConsumerContainerConfig(marketStreamConsumerContainerConfigGSSP)
                .setMonitorDispatcher(new MantisMonitorDispatcher(performanceDisruptor))
                .build();
    }

    @Bean
    public MarketStreamClient marketStreamClientFMG(@Value("${msct.market.stream.client.config.fmg}") final String marketStreamClientConfigFMG,
                                                    @Value("${msct.market.stream.consumer.config.fmg}") final String marketStreamConsumerConfigFMG,
                                                    @Value("${msct.market.stream.consumer.container.config.fmg}") final String marketStreamConsumerContainerConfigFMG,
                                                    @Autowired final PerformanceDisruptor performanceDisruptor) throws Exception {
        return new MarketStreamClient.MarketStreamClientBuilder(marketStreamClientConfigFMG)
                .setProvidedConsumerConfig(marketStreamConsumerConfigFMG)
                .setProvidedConsumerContainerConfig(marketStreamConsumerContainerConfigFMG)
                .setMonitorDispatcher(new MantisMonitorDispatcher(performanceDisruptor))
                .build();
    }


    @Bean
    public FeatureToggle marketStreamClientToggleGSSP(@Value("${msct.market.stream.client.enabled.gssp}") final boolean marketStreamClientEnabledGSSP) {
        return new SimpleFeatureToggle(marketStreamClientEnabledGSSP, "marketStreamClientGSSP");
    }

    @Bean
    public FeatureToggle marketStreamClientToggleFMG(@Value("${msct.market.stream.client.enabled.fmg}") final boolean marketStreamClientEnabledFMG) {
        return new SimpleFeatureToggle(marketStreamClientEnabledFMG, "marketStreamClientFMG");
    }

    @Bean
    public PropertySourcesPlaceholderConfigurer propertySourcesPlaceholderConfigurer() {
        PropertySourcesPlaceholderConfigurer configurer = new PropertySourcesPlaceholderConfigurer();
        configurer.setLocation(new ClassPathResource("application.properties"));
        return configurer;
    }

    @Bean
    public Set<ServiceDataEventHandler> performanceEventHandlers(ServiceDataEventHandler services) {
        ServiceDataEventHandler handler = new ServiceDataEventHandler(new ClassBasedPredicate(SERVICE));
        handler.setMaxAge(300000);
        handler.setMaxSize(16000);

        Set<ServiceDataEventHandler> set = new HashSet<>();
        set.add(services);
        return set;
    }

    @Bean
    public Set<Class<?>> metricTags() {
        return new HashSet<>();
    }



}
