# GSSP Client/Consumer/Container config locations
msct.market.stream.client.config.gssp=conf/market-stream-client-gssp.properties
msct.market.stream.consumer.config.gssp=conf/consumer-gssp.conf
msct.market.stream.consumer.container.config.gssp=conf/consumer-container-gssp.conf

# FMG Client/Consumer/Container config locations
msct.market.stream.client.config.fmg=conf/market-stream-client-fmg.properties
msct.market.stream.consumer.config.fmg=conf/consumer-fmg.conf
msct.market.stream.consumer.container.config.fmg=conf/consumer-container-fmg.conf

# Feature toggles
msct.market.stream.client.enabled.gssp=true
msct.market.stream.client.enabled.fmg=true

spring.jmx.enabled=true

management.endpoints.web.exposure.include=*
management.endpoints.jmx.exposure.include=*
management.endpoint.health.show-details=always

hawtio.authenticationEnabled=false

test.name=Hello World

config.gssp=12321

tracing.level=10
tracing.buffer.size=2048
tracing.stats.age=300000
tracing.stats.size=16000
statse.agent.address=tcp://127.0.0.1:14444
statse.queue.size=1000
statse.enabled=false


